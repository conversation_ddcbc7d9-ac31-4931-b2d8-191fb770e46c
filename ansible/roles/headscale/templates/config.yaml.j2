---
# headscale will look for a configuration file named `config.yaml` (or `config.json`) in the following order:
#
# - `/etc/headscale/`
# - `~/.headscale/`
# - current working directory

# The url clients will connect to.
# Typically this will be a domain like:
#
# https://headscale.example.com:443
#
server_url: https://{{ headscale_domain | default('headscale.' + ansible_fqdn) }}

# Address to listen to / bind to on the server
#
# For production:
# listen_addr: 0.0.0.0:8080
listen_addr: 0.0.0.0:8080

# Address to listen to /metrics, you may want
# to keep this endpoint private to your internal
# network
#
metrics_listen_addr: 127.0.0.1:9090

# Address to listen for gRPC.
# gRPC is used for controlling a headscale server
# remotely with the CLI
# Note: Remote access _only_ works if you have
# valid certificates.
grpc_listen_addr: 0.0.0.0:50443

# Allow the gRPC admin interface to run in INSECURE
# mode. This is not recommended as the traffic will
# be unencrypted. Only enable if you know what you
# are doing.
grpc_allow_insecure: false

# Private key used to encrypt the traffic between headscale
# and Tailscale clients.
# The private key file will be autogenerated if it's missing.
#
private_key_path: /var/lib/headscale/private.key

# The Noise section includes specific configuration for the
# Noise protocol connection used between headscale and
# Tailscale clients for end-to-end encrypted sessions.
noise:
  # The Noise private key is used to encrypt the
  # traffic between headscale and Tailscale clients when
  # using the new Noise-based protocol.
  private_key_path: /var/lib/headscale/noise_private.key

# List of IP prefixes to allocate tailaddresses from.
# Each prefix consists of either an IPv4 or IPv6 CIDR prefix.
# The default is **********/10 for IPv4 and fd7a:115c:a1e0::/48 for IPv6.
prefixes:
  v4: **********/10
  v6: fd7a:115c:a1e0::/48

# DERP is a relay system that Tailscale uses when a direct
# connection cannot be established.
# https://tailscale.com/blog/how-tailscale-works/#encrypted-tcp-relays-derp
#
# headscale needs a list of DERP servers that can be presented
# to the clients.
derp:
  server:
    # If enabled, runs an embedded DERP server at the listen address.
    enabled: true

    # Region ID to use for the embedded DERP server.
    # The local DERP server will be preferred over
    # the public Tailscale DERP servers.
    region_id: 999

    # Region code and name are displayed in the Tailscale UI to identify a DERP region
    region_code: "headscale"
    region_name: "Headscale Embedded DERP"

    # Hostnames that the DERP server will respond to.
    # The DERP server will use these hostnames to generate the URLs
    # that clients can use to connect to the DERP server.
    stun_listen_addr: "0.0.0.0:3478"

    # Private key used to encrypt the traffic between headscale DERP and
    # Tailscale clients. A missing key will be automatically generated.
    private_key_path: /var/lib/headscale/derp_server_private.key

    # This flag can be used, so the DERP map entry for the embedded DERP server is not written automatically,
    # it enables the creation of your very own DERP map entry using a locally available file with the parameter DERP.paths
    # If you enable the DERP server and set this to false, it is required to add the DERP server to the DERP map using DERP.paths
    automatically_add_embedded_derp_region: true

  # List of externally available DERP maps encoded in JSON
  urls:
    - https://controlplane.tailscale.com/derpmap/default

  # Locally available DERP map files encoded in YAML
  #
  # This option is mostly interesting for people hosting
  # their own DERP servers:
  # https://tailscale.com/kb/1118/custom-derp-servers/
  #
  # paths:
  #   - /etc/headscale/derp-example.yaml
  paths: []

  # If enabled, a worker will be set up to periodically
  # refresh the given sources and update the derpmap
  # will be set up.
  auto_update_enabled: true

  # How often should we check for DERP updates?
  update_frequency: 24h

# Disables the automatic check for headscale updates on startup
disable_check_updates: false

# Time before an inactive ephemeral node is deleted?
ephemeral_node_inactivity_timeout: 30m

# Period to check for node updates within the tailnet. A value too low will severely affect
# CPU consumption of Headscale. A value too high (over 60s) will cause problems
# for the nodes, as they won't get updates or keep alive messages frequently enough.
# In case of doubts, do not touch the default 10s.
node_update_check_interval: 10s

# SQLite config
database:
  # Database type. Available options: sqlite, postgres
  # Please note that using Postgres is highly discouraged as it is only supported for legacy reasons.
  # All new development, testing and optimisations are done with SQLite in mind.
  type: sqlite

  # Enable debug mode. This setting requires the log.level to be set to "debug" or "trace".
  debug: false

  # SQLite config
  sqlite:
    path: /var/lib/headscale/db.sqlite

### TLS configuration
#
## Let's encrypt / ACME
#
# headscale supports automatically requesting and setting up
# TLS for a domain with Let's Encrypt.
#
# URL to ACME directory
acme_url: https://acme-v02.api.letsencrypt.org/directory

# Email to register with ACME provider
acme_email: "{{ headscale_acme_email | default('admin@' + ansible_domain) }}"

# Domain name to request a TLS certificate for:
tls_letsencrypt_hostname: "{{ headscale_domain | default('headscale.' + ansible_fqdn) }}"

# Path to store certificates and metadata needed by
# letsencrypt
# For production:
tls_letsencrypt_cache_dir: /var/lib/headscale/cache

# Type of ACME challenge to use, currently supported types:
# HTTP-01 or TLS-ALPN-01
# See [docs/tls.md](docs/tls.md) for more information
tls_letsencrypt_challenge_type: HTTP-01
# When HTTP-01 challenge is chosen, letsencrypt must set up a
# verification endpoint, and it will be listening on:
# :http = port 80
tls_letsencrypt_listen: ":http"

## Use already defined certificates:
# tls_cert_path: ""
# tls_key_path: ""

log:
  # Output formatting for logs: text or json
  format: text
  level: info

# Path to a file containg ACL policies.
# ACLs work on the basis of users. Tailscale defines a user as 'someone who is authenticated by your chosen authentication provider'.
# Thus, this is likely a GitHub/GitLab/Google/etc. user.
# Tailscale then uses that to create a 'tailnet', a group of devices.
# There can be multiple tailnets in a Headscale deployment, the name will be shown under the Machines page, and is used to generate the MagicDNS name.
# The ACL can be defined as either a file or inline.
#
# acl_policy_path: ""
policy:
  # The mode for the ACL policy. Can be "file" or "database".
  # "file" will read the ACL policy from the file specified in the acl_policy_path field.
  # "database" will read the ACL policy from the database.
  mode: file
  path: ""

## DNS
#
# headscale supports Tailscale's DNS configuration and MagicDNS.
# Please have a look to their KB to better understand the concepts:
#
# - https://tailscale.com/kb/1054/dns/
# - https://tailscale.com/kb/1081/magicdns/
# - https://tailscale.com/blog/2021-09-private-dns-with-magicdns/
#
dns:
  # Whether to prefer using Headscale provided DNS or use local system resolver
  override_local_dns: false

  # List of DNS servers to expose to clients.
  nameservers:
    - *******
    - *******

  # NextDNS (see https://tailscale.com/kb/1218/nextdns/).
  # "abc123" is example NextDNS ID, replace with yours.
  #
  # With metadata sharing:
  # nameservers:
  #   - https://dns.nextdns.io/abc123
  #
  # Without metadata sharing:
  # nameservers:
  #   - 2a07:a8c0::ab:c123
  #   - 2a07:a8c1::ab:c123

  # Split DNS (see https://tailscale.com/kb/1054/dns/),
  # list of search domains and the DNS to query for each one.
  #
  # restricted_nameservers:
  #   foo.bar.com:
  #     - *******
  #   darp.headscale.net:
  #     - *******
  #     - *******

  # Search domains to inject.
  domains: []

  # Extra DNS records
  # so far only A-records are supported (on the tailscale side)
  # See https://github.com/juanfont/headscale/blob/main/docs/dns-records.md#Limitations
  # extra_records:
  #   - name: "grafana.myvpn.example.com"
  #     type: "A"
  #     value: "**********"
  #
  #   # you can also put it in one line
  #   - { name: "prometheus.myvpn.example.com", type: "A", value: "**********" }

  # Whether to use [MagicDNS](https://tailscale.com/kb/1081/magicdns/).
  # Only works if there is at least a nameserver defined.
  magic_dns: true

  # Defines the base domain to create the hostnames for MagicDNS.
  # `base_domain` must be a FQDNs, without the trailing dot.
  # The FQDN of the hosts will be
  # `hostname.user.base_domain` (e.g., _myhost.myuser.example.com_).
  base_domain: {{ headscale_base_domain | default('headscale.local') }}

# Unix socket used for the CLI to connect without authentication
# Note: for production you will want to set this to something like:
unix_socket: /var/run/headscale/headscale.sock
unix_socket_permission: "0770"
#
# headscale supports experimental OpenID connect support,
# it is still being tested and might have some bugs, please
# help us test it.
# OpenID Connect
# oidc:
#   only_start_if_oidc_is_available: true
#   issuer: "https://your-oidc.issuer.com/path"
#   client_id: "your-oidc-client-id"
#   client_secret: "your-oidc-client-secret"
#   client_secret_path: "/path/to/client_secret_file"
#   # Alternatively, set `client_secret_env_var` to read the secret from an environment variable:
#   # client_secret_env_var: OIDC_CLIENT_SECRET
#   scope: ["openid", "profile", "email"]
#   extra_params: {}
#   # List allowed principal domains and/or users. If an authenticated user's domain is not in this list, the
#   # authentication request will be rejected.
#   allowed_domains: []
#   allowed_users: []
#   # If `strip_email_domain` is set to `true`, the domain part of the username email address will be removed.
#   # This will transform `<EMAIL>` to `first-name.last-name`
#   # If `strip_email_domain` is set to `false`, the domain part will NOT be removed resulting to the following
#   username: `first-name.last-name.example.com`
#   strip_email_domain: true

# Logtail configuration
# Logtail is Tailscale's logging and auditing infrastructure.
# ref: https://tailscale.com/kb/1021/install-logtail/
# Note: Logtail is only available if you have a (paid) Tailscale account.
#
# logtail:
#   enabled: false

# Enabling this option makes devices prefer a random port for WireGuard traffic over the
# default static port 41641. This option is intended as a workaround for some buggy
# firewall, NAT, and router implementations that can interfere with and block Tailscale
# traffic. Using a random port makes it harder to implement port-based rules and filters,
# but this option should only be used as a last resort by users that cannot get Tailscale
# to work otherwise.
randomize_client_port: false
